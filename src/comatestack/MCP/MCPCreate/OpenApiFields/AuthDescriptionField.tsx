import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useEffect, useRef} from 'react';
import {setSystemUpdating} from '../../MCPEdit/hooks';

interface Props {
    path?: Path;
}

const AuthDescription = ({path = []}: Props) => {
    const form = Form.useFormInstance();
    const authType = useWatch([...path, 'serverConf', 'serverExtension', 'serverAuthType'], form);
    const prevAuthTypeRef = useRef(authType);
    const authDescription = useWatch([...path, 'serverConf', 'serverExtension', 'authDescription'], form);
    const initialAuthDescriptionRef = useRef<string | undefined>(undefined);
    const hasInitializedRef = useRef(false);

    const defaultCloudIamContent = '根据AccessKey SecretKey进行认证，获取方式参考 云上百度（百度云度厂版）相关文档 https://cloud.baidu-int.com/icloud/help/%E4%BC%81%E4%B8%9A%E7%BB%84%E7%BB%87/%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/';

    useEffect(
        () => {
            if (!hasInitializedRef.current && authDescription !== undefined
                && authDescription !== '' && authDescription !== null && authDescription !== defaultCloudIamContent) {
                initialAuthDescriptionRef.current = authDescription;
                hasInitializedRef.current = true;
            }
        },
        [authDescription, defaultCloudIamContent]
    );

    const getNewValueForAuthType = (currentValue: string | undefined, hasUserCustomContent: boolean, targetAuthType: string) => {
        const isEmptyOrDefault = !currentValue || currentValue === '' || currentValue === defaultCloudIamContent;

        if (isEmptyOrDefault) {
            return targetAuthType === 'CLOUD_INIT_IAM' ? defaultCloudIamContent : undefined;
        }

        if (hasUserCustomContent) {
            return currentValue;
        }

        return targetAuthType === 'CLOUD_INIT_IAM' ? defaultCloudIamContent : undefined;
    };

    useEffect(
        () => {
            const fieldPath = [...path, 'serverConf', 'serverExtension', 'authDescription'];
            const prevAuthType = prevAuthTypeRef.current;

            if (authType !== prevAuthType && prevAuthType !== undefined) {
                const hasUserCustomContent = initialAuthDescriptionRef.current !== undefined
                    && initialAuthDescriptionRef.current !== null
                    && initialAuthDescriptionRef.current !== '';

                const newValue = getNewValueForAuthType(authDescription, hasUserCustomContent, authType);

                setSystemUpdating(true);
                try {
                    form.setFieldValue(fieldPath, newValue);
                } finally {
                    setSystemUpdating(false);
                }
            }
            prevAuthTypeRef.current = authType;
        },
        [authType, authDescription, form, path, defaultCloudIamContent]
    );

    if (authType === 'NONE') {
        return null;
    }

    const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';
    const placeholder = authType === 'OTHER'
        ? '请说明鉴权的方法以及获取鉴权凭证的方式'
        : '请输入鉴权方法';

    return (
        <Form.Item
            label="鉴权方法"
            name={[...path, 'serverConf', 'serverExtension', 'authDescription']}
            rules={isRequired ? [{required: true, message: '请输入鉴权方法'}] : []}
            validateTrigger={['onBlur', 'onChange']}
        >
            <Input.TextArea
                placeholder={placeholder}
                autoSize={{minRows: 3}}
            />
        </Form.Item>
    );
};

export default AuthDescription;

